using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Xunit;
using Xunit.Abstractions;
using NopCommerceTests.Services;
using NopCommerceTests.Configuration;

namespace NopCommerceTests;

public class NopCommerceTestsWithLlamaGuard : IDisposable
{
    private readonly IWebDriver _driver;
    private readonly WebDriverWait _wait;
    private readonly LlamaGuardService _llamaGuard;
    private readonly LlamaGuardConfig _config;
    private readonly ITestOutputHelper _output;
    
    private const string BaseUrl = "http://localhost:90";
    private const int TimeoutSeconds = 30;

    public NopCommerceTestsWithLlamaGuard(ITestOutputHelper output)
    {
        _output = output;
        
        // Configure Chrome options for headless mode
        var chromeOptions = new ChromeOptions();
        chromeOptions.AddArgument("--headless");
        chromeOptions.AddArgument("--no-sandbox");
        chromeOptions.AddArgument("--disable-dev-shm-usage");
        chromeOptions.AddArgument("--disable-gpu");
        chromeOptions.AddArgument("--window-size=1920,1080");

        _driver = new ChromeDriver(chromeOptions);
        _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(TimeoutSeconds));

        // Initialize Llama-Guard service
        _config = new LlamaGuardConfig();
        var httpClient = new HttpClient();
        httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
        _llamaGuard = new LlamaGuardService(httpClient, _config.BaseUrl, _config.ModelName);
    }

    [Fact]
    public async Task HomePageUserJourney_WithContentModeration_ShouldVerifyTitleLogoAndContentSafety()
    {
        // Step 1: Open the home page
        _driver.Navigate().GoToUrl(BaseUrl);
        _wait.Until(driver => driver.Title.Length > 0);

        // Step 2: Verify the title
        var actualTitle = _driver.Title;
        Assert.Equal("Your store. Home page title", actualTitle);

        // Step 3: Verify logo alt text
        var logoElement = _wait.Until(driver => 
            driver.FindElement(By.CssSelector(".header-logo img")));
        var altText = logoElement.GetAttribute("alt");
        Assert.Equal("Your store name", altText);

        // Step 4: Content Safety Analysis with Llama-Guard
        await PerformContentSafetyAnalysis();
    }

    [Fact]
    public async Task AnalyzePageContent_ShouldDetectUnsafeContent()
    {
        // Navigate to the page
        _driver.Navigate().GoToUrl(BaseUrl);
        _wait.Until(driver => driver.Title.Length > 0);

        // Extract and analyze different types of content
        var contentAnalyses = new List<Task<ContentModerationResult>>();

        // Analyze page title
        var title = _driver.Title;
        contentAnalyses.Add(_llamaGuard.AnalyzeContentAsync(title, "page_title"));

        // Analyze main page text content
        var bodyText = ExtractCleanPageText();
        if (!string.IsNullOrEmpty(bodyText))
        {
            contentAnalyses.Add(_llamaGuard.AnalyzeContentAsync(bodyText, "page_content"));
        }

        // Analyze navigation menu text
        var navText = ExtractNavigationText();
        if (!string.IsNullOrEmpty(navText))
        {
            contentAnalyses.Add(_llamaGuard.AnalyzeContentAsync(navText, "navigation"));
        }

        // Wait for all analyses to complete
        var results = await Task.WhenAll(contentAnalyses);

        // Log results
        foreach (var result in results)
        {
            LogModerationResult(result);
        }

        // Assert that all content is safe (or handle according to configuration)
        if (_config.FailOnUnsafeContent)
        {
            var unsafeResults = results.Where(r => !r.IsSafe).ToList();
            if (unsafeResults.Any())
            {
                var unsafeContent = string.Join(", ", unsafeResults.Select(r => r.Reason));
                Assert.True(false, $"Unsafe content detected: {unsafeContent}");
            }
        }

        // At minimum, ensure we got results
        Assert.True(results.Length > 0, "No content was analyzed");
        Assert.All(results, result => Assert.NotNull(result));
    }

    [Fact]
    public async Task TestSpecificContent_ShouldIdentifyProblematicContent()
    {
        // Test with known safe content
        var safeResult = await _llamaGuard.AnalyzeContentAsync("Welcome to our online store! We sell computers and electronics.", "test_content");
        Assert.True(safeResult.IsSafe, $"Safe content was flagged as unsafe: {safeResult.Reason}");

        // Test with potentially problematic content (this should be safe but tests the system)
        var testResult = await _llamaGuard.AnalyzeContentAsync("Click here to buy now! Limited time offer!", "test_content");
        LogModerationResult(testResult);

        // Verify the service is working
        Assert.NotNull(testResult);
        Assert.NotEmpty(testResult.RawResponse);
    }

    private async Task PerformContentSafetyAnalysis()
    {
        if (!_config.EnableModeration)
        {
            _output.WriteLine("Content moderation is disabled in configuration");
            return;
        }

        try
        {
            // Extract various content types for analysis
            var contentToAnalyze = new Dictionary<string, string>();

            // Page title
            contentToAnalyze["title"] = _driver.Title;

            // Main content
            var mainContent = ExtractCleanPageText();
            if (!string.IsNullOrEmpty(mainContent))
            {
                contentToAnalyze["main_content"] = TruncateContent(mainContent);
            }

            // Navigation
            var navContent = ExtractNavigationText();
            if (!string.IsNullOrEmpty(navContent))
            {
                contentToAnalyze["navigation"] = navContent;
            }

            // Analyze all content
            var analysisResults = new List<ContentModerationResult>();
            foreach (var content in contentToAnalyze)
            {
                var result = await _llamaGuard.AnalyzeContentAsync(content.Value, content.Key);
                analysisResults.Add(result);
                LogModerationResult(result, content.Key);
            }

            // Check for any unsafe content
            var unsafeResults = analysisResults.Where(r => !r.IsSafe).ToList();
            if (unsafeResults.Any() && _config.FailOnUnsafeContent)
            {
                var issues = string.Join("; ", unsafeResults.Select(r => $"{r.Reason}"));
                throw new Exception($"Content safety violations detected: {issues}");
            }

            _output.WriteLine($"Content safety analysis completed. Analyzed {analysisResults.Count} content pieces. " +
                            $"Safe: {analysisResults.Count(r => r.IsSafe)}, Unsafe: {analysisResults.Count(r => !r.IsSafe)}");
        }
        catch (Exception ex)
        {
            _output.WriteLine($"Content safety analysis failed: {ex.Message}");
            if (_config.FailOnUnsafeContent)
            {
                throw;
            }
        }
    }

    private string ExtractCleanPageText()
    {
        try
        {
            // Get main content, excluding scripts, styles, and hidden elements
            var script = @"
                var elements = document.querySelectorAll('script, style, .hidden, [style*=""display: none""]');
                elements.forEach(el => el.remove());
                return document.body.innerText || document.body.textContent || '';
            ";
            
            var text = ((IJavaScriptExecutor)_driver).ExecuteScript(script)?.ToString() ?? "";
            return CleanText(text);
        }
        catch
        {
            return "";
        }
    }

    private string ExtractNavigationText()
    {
        try
        {
            var navElements = _driver.FindElements(By.CssSelector("nav, .navigation, .menu, .top-menu"));
            var navTexts = navElements.Select(el => el.Text).Where(text => !string.IsNullOrWhiteSpace(text));
            return string.Join(" ", navTexts);
        }
        catch
        {
            return "";
        }
    }

    private string CleanText(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return "";

        // Remove extra whitespace and normalize
        return System.Text.RegularExpressions.Regex.Replace(text.Trim(), @"\s+", " ");
    }

    private string TruncateContent(string content)
    {
        if (content.Length <= _config.MaxContentLength)
            return content;

        return content.Substring(0, _config.MaxContentLength) + "...";
    }

    private void LogModerationResult(ContentModerationResult result, string contentType = "content")
    {
        if (!_config.LogResults)
            return;

        var status = result.IsSafe ? "SAFE" : "UNSAFE";
        var confidence = $"{result.Confidence:P1}";
        
        _output.WriteLine($"[{status}] {contentType} ({confidence}): {result.Reason}");
        
        if (!result.IsSafe && result.Categories.Any())
        {
            _output.WriteLine($"  Categories: {string.Join(", ", result.Categories)}");
        }

        if (!string.IsNullOrEmpty(result.RawResponse))
        {
            _output.WriteLine($"  Raw response: {result.RawResponse}");
        }
    }

    public void Dispose()
    {
        _driver?.Quit();
        _driver?.Dispose();
    }
}
