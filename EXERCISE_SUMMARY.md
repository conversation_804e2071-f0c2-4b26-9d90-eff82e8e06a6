# nopCommerce Selenium Testing Exercise - Summary

## 🎯 What Students Will Build

### Part 1: Basic Selenium Test (Required)
Students will complete a comprehensive Selenium WebDriver test that:

1. **Initializes ChromeDriver** with proper configuration
2. **Navigates to nopCommerce** homepage at http://localhost:90
3. **Verifies page title** matches "Your store. Home page title"
4. **Locates logo element** using CSS selector `.header-logo img`
5. **Verifies alt text** equals "Your store name"
6. **Implements proper cleanup** with driver.Quit()

### Part 2: AI Integration (Optional Bonus)
Advanced students can extend with Llama-Guard AI content moderation:

1. **Test connectivity** to local Ollama instance
2. **Analyze e-commerce content** for safety
3. **Implement batch processing** for multiple content pieces
4. **Use context-aware analysis** for different content types
5. **Measure performance** of AI operations

## 📁 File Structure

```
NopCommerceTests/
├── UnitTest1.cs                    # Main exercise file (students complete this)
├── LlamaGuardExercises.cs          # AI integration exercises (optional)
├── NopCommerceTestsWithLlamaGuard.cs  # Complete example (reference)
├── Services/
│   └── LlamaGuardService.cs        # AI service implementation
├── Configuration/
│   └── LlamaGuardConfig.cs         # Configuration settings
└── README.md                       # Complete instructions
```

## 🔧 Student Tasks

### Required Tasks (UnitTest1.cs)
- [ ] Complete `InitialiseWebDriver()` method
- [ ] Complete `OpenNopCommercePage()` method  
- [ ] Complete `VerifyPageTitle()` method
- [ ] Complete `VerifyLogoAltText()` method
- [ ] Test the complete user journey

### Optional Tasks (LlamaGuardExercises.cs)
- [ ] Exercise 1: Test Llama-Guard connectivity
- [ ] Exercise 2: Analyze e-commerce content safety
- [ ] Exercise 3: Implement batch content analysis
- [ ] Exercise 4: Context-aware content analysis
- [ ] Exercise 5: Performance testing

## 🚀 Quick Start for Students

1. **Start nopCommerce**:
   ```bash
   docker compose up -d
   ```

2. **Open the project** in your IDE

3. **Complete the methods** in `UnitTest1.cs` following the TODO comments

4. **Run your test**:
   ```bash
   dotnet test --filter "HomePageUserJourney_ShouldVerifyTitleAndLogoAltText"
   ```

5. **For advanced students**, set up Ollama and complete `LlamaGuardExercises.cs`

## ✅ Success Criteria

### Part 1 (Required)
- ✅ Test passes without errors
- ✅ Page title is correctly verified
- ✅ Logo alt text is correctly verified
- ✅ Code follows the provided pattern
- ✅ Proper error handling and cleanup

### Part 2 (Bonus)
- ✅ All 5 Llama-Guard exercises pass
- ✅ Proper async/await usage
- ✅ Meaningful test output and logging
- ✅ Performance requirements met

## 🎓 Learning Outcomes

Students will learn:
- **Selenium WebDriver fundamentals**
- **Test automation best practices**
- **Async programming in C#**
- **AI service integration**
- **Performance testing concepts**
- **Clean code principles**

## 📚 Resources

- Complete solution examples in README.md
- Troubleshooting guide for common issues
- Links to official documentation
- Assessment criteria and grading rubric

---

**Estimated Time**: 2-4 hours (Part 1: 1-2 hours, Part 2: 1-2 hours)
**Difficulty**: Beginner to Intermediate
**Prerequisites**: Basic C# knowledge, understanding of web technologies
