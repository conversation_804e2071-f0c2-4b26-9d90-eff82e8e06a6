# nopCommerce Selenium Testing Exercise

This is a hands-on exercise for learning Selenium WebDriver automation testing with nopCommerce e-commerce platform. Students will implement automated tests and optionally integrate AI-powered content moderation using Llama-Guard.

## 🎯 Learning Objectives

By completing this exercise, you will learn:
- How to set up Selenium WebDriver with .NET 8
- Writing automated UI tests for web applications
- Implementing the Page Object Model pattern
- Using explicit waits and element locators
- Integrating AI content moderation in tests
- Best practices for test automation

## 📋 Exercise Overview

### Part 1: Basic Selenium Tests (Required)
Students will implement automated tests for the nopCommerce homepage:
1. Navigate to the homepage
2. Verify page title
3. Locate and verify logo alt text
4. Implement proper WebDriver initialization and cleanup

### Part 2: Advanced Integration (Optional)
Students can extend their tests with AI-powered content moderation:
1. Set up local Ollama with Llama-Guard
2. Extract page content during tests
3. Analyze content safety using AI
4. Log moderation results

## 📚 Prerequisites

### Required (All Students)
1. **.NET 8 SDK** - Download from [Microsoft](https://dotnet.microsoft.com/download/dotnet/8.0)
2. **Docker** - For running nopCommerce ([Docker Desktop](https://www.docker.com/products/docker-desktop))
3. **Chrome Browser** - Tests use ChromeDriver
4. **Visual Studio Code or Visual Studio** - IDE for development

### Optional (Advanced Students)
5. **Ollama** - For AI content moderation ([Ollama.ai](https://ollama.ai))
6. **Llama-Guard Model** - Downloaded via Ollama

## 🚀 Getting Started

### Step 1: Environment Setup

1. **Clone or download this repository**
2. **Start nopCommerce**:
   ```bash
   docker compose up -d
   ```
   Wait for nopCommerce to fully start (2-3 minutes on first run)

3. **Verify nopCommerce is running**:
   - Open http://localhost:90 in your browser
   - You should see the nopCommerce homepage

### Step 2: Project Structure
```
NopCommerceTests/
├── UnitTest1.cs                    # Your main test file (TO COMPLETE)
├── NopCommerceTestsWithLlamaGuard.cs  # Advanced AI integration example
├── Services/
│   └── LlamaGuardService.cs        # AI content moderation service
├── Configuration/
│   └── LlamaGuardConfig.cs         # Configuration for AI features
└── NopCommerceTests.csproj         # Project dependencies
```

## 📝 Exercise Instructions

### Part 1: Basic Selenium Test Implementation

#### Task 1.1: Complete the InitialiseWebDriver Method
Open `UnitTest1.cs` and complete the `InitialiseWebDriver()` method:

**Requirements:**
- Create ChromeOptions with headless mode enabled
- Add necessary Chrome arguments for stability
- Create and return a ChromeDriver instance
- Set implicit wait timeout to 500 milliseconds

**Hints:**
- Use `chromeOptions.AddArgument("--headless")`
- Add `--no-sandbox` and `--disable-dev-shm-usage` for Linux compatibility
- Use `driver.Manage().Timeouts().ImplicitWait`

#### Task 1.2: Implement OpenNopCommercePage Method
Complete the method to navigate to nopCommerce and wait for page load:

**Requirements:**
- Navigate to `http://localhost:90`
- Wait for the page title to be loaded (length > 0)
- Use WebDriverWait with 30-second timeout

**Hints:**
- Use `driver.Navigate().GoToUrl()`
- Create WebDriverWait: `new WebDriverWait(driver, TimeSpan.FromSeconds(30))`
- Wait condition: `wait.Until(d => d.Title.Length > 0)`

#### Task 1.3: Implement VerifyPageTitle Method
Complete the method to verify the page title:

**Requirements:**
- Get the current page title
- Assert it equals "Your store. Home page title"
- Use xUnit Assert.Equal

#### Task 1.4: Implement VerifyLogoAltText Method
Complete the method to find and verify the logo:

**Requirements:**
- Wait for logo element to be present using CSS selector `.header-logo img`
- Get the `alt` attribute value
- Assert it equals "Your store name"
- Bonus: Verify the `src` attribute contains "logo.png"

**Hints:**
- Use `wait.Until(driver => driver.FindElement(By.CssSelector(".header-logo img")))`
- Get attribute: `element.GetAttribute("alt")`

#### Task 1.5: Complete the Main Test Method
Ensure the test method follows this pattern:
```csharp
[Fact]
public void HomePageUserJourney_ShouldVerifyTitleAndLogoAltText()
{
    IWebDriver driver = InitialiseWebDriver();

    OpenNopCommercePage(driver);
    VerifyPageTitle(driver, "Your store. Home page title");
    VerifyLogoAltText(driver, "Your store name");

    driver.Quit();
}
```

### Testing Your Implementation

Run your test to verify it works:
```bash
cd NopCommerceTests
dotnet test --filter "HomePageUserJourney_ShouldVerifyTitleAndLogoAltText" --verbosity normal
```

**Expected Result:** ✅ Test should pass and verify both title and logo alt text.

---

## 🤖 Part 2: Advanced AI Integration (Optional)

### Prerequisites for Advanced Exercises
1. **Install Ollama**:
   ```bash
   # Install Ollama
   curl -fsSL https://ollama.ai/install.sh | sh

   # Start Ollama service
   ollama serve
   ```

2. **Download Llama-Guard model**:
   ```bash
   # Download the lightweight 1B model (recommended)
   ollama pull llama-guard3:1b
   ```

3. **Verify Ollama is working**:
   ```bash
   ollama list
   # Should show llama-guard3:1b in the list
   ```

### Advanced Exercise Instructions

#### Exercise 2.1: Basic Llama-Guard Connectivity
Open `LlamaGuardExercises.cs` and complete `Exercise1_TestLlamaGuardConnectivity()`:

**Requirements:**
- Test basic connectivity to Llama-Guard
- Analyze a simple safe message
- Verify the result is marked as safe
- Log the analysis result

#### Exercise 2.2: E-commerce Content Testing
Complete `Exercise2_TestEcommerceContent()`:

**Requirements:**
- Test multiple e-commerce phrases using Theory and InlineData
- Verify all typical e-commerce content is considered safe
- Handle async operations properly

#### Exercise 2.3: Batch Content Analysis
Complete `Exercise3_BatchContentAnalysis()`:

**Requirements:**
- Create a list of multiple content strings
- Use batch analysis for better performance
- Verify all results are safe
- Log batch processing results

#### Exercise 2.4: Context-Aware Analysis
Complete `Exercise4_ContentWithContext()`:

**Requirements:**
- Analyze the same content with different contexts
- Compare how context affects analysis
- Demonstrate context-aware content moderation

#### Exercise 2.5: Performance Testing
Complete `Exercise5_PerformanceTest()`:

**Requirements:**
- Measure analysis execution time
- Ensure performance meets requirements
- Use proper timing mechanisms

### Testing Advanced Exercises

Run the Llama-Guard exercises:
```bash
cd NopCommerceTests
# Test connectivity first
dotnet test --filter "Exercise1_TestLlamaGuardConnectivity" --verbosity normal

# Run all Llama-Guard exercises
dotnet test --filter "LlamaGuardExercises" --verbosity normal
```

## 📚 Solution Guide

### Part 1 Solutions

<details>
<summary>Click to view InitialiseWebDriver solution</summary>

```csharp
private static IWebDriver InitialiseWebDriver()
{
    var chromeOptions = new ChromeOptions();
    chromeOptions.AddArgument("--headless");
    chromeOptions.AddArgument("--no-sandbox");
    chromeOptions.AddArgument("--disable-dev-shm-usage");
    chromeOptions.AddArgument("--disable-gpu");
    chromeOptions.AddArgument("--window-size=1920,1080");

    IWebDriver driver = new ChromeDriver(chromeOptions);
    driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromMilliseconds(500);
    return driver;
}
```
</details>

<details>
<summary>Click to view OpenNopCommercePage solution</summary>

```csharp
private static void OpenNopCommercePage(IWebDriver driver)
{
    driver.Navigate().GoToUrl(BaseUrl);
    var wait = new WebDriverWait(driver, TimeSpan.FromSeconds(TimeoutSeconds));
    wait.Until(d => d.Title.Length > 0);
}
```
</details>

<details>
<summary>Click to view VerifyPageTitle solution</summary>

```csharp
private static void VerifyPageTitle(IWebDriver driver, string expectedTitle)
{
    var actualTitle = driver.Title;
    Assert.Equal(expectedTitle, actualTitle);
}
```
</details>

<details>
<summary>Click to view VerifyLogoAltText solution</summary>

```csharp
private static void VerifyLogoAltText(IWebDriver driver, string expectedAltText)
{
    var wait = new WebDriverWait(driver, TimeSpan.FromSeconds(TimeoutSeconds));
    var logoElement = wait.Until(driver =>
        driver.FindElement(By.CssSelector(".header-logo img")));

    var altText = logoElement.GetAttribute("alt");
    Assert.Equal(expectedAltText, altText);

    // Bonus verification
    var logoSrc = logoElement.GetAttribute("src");
    Assert.Contains("logo.png", logoSrc);
}
```
</details>

### Part 2 Solutions

<details>
<summary>Click to view Exercise 1 solution</summary>

```csharp
[Fact]
public async Task Exercise1_TestLlamaGuardConnectivity()
{
    var testContent = "Hello, this is a test message";
    var result = await _llamaGuard.AnalyzeContentAsync(testContent);

    Assert.NotNull(result);
    Assert.True(result.IsSafe);
    _output.WriteLine($"Content: {testContent}");
    _output.WriteLine($"Safe: {result.IsSafe}, Confidence: {result.Confidence:P1}");
}
```
</details>

## 🔧 Troubleshooting

### Common Issues

#### Part 1 Issues (Basic Selenium)

**Problem**: Test fails with "session not created" error
**Solution**:
- Ensure Chrome browser is installed
- Try running with visible browser (remove `--headless` argument)
- Check if another Chrome instance is running

**Problem**: Test fails with timeout waiting for page
**Solution**:
- Verify nopCommerce is running: `docker compose ps`
- Check if http://localhost:90 is accessible in browser
- Increase timeout if nopCommerce is slow to start

**Problem**: Logo element not found
**Solution**:
- Verify the CSS selector `.header-logo img` is correct
- Check if page has fully loaded before searching for element
- Use browser developer tools to inspect the actual HTML structure

#### Part 2 Issues (Llama-Guard)

**Problem**: Ollama connection fails
**Solution**:
```bash
# Check if Ollama is running
ollama list

# Start Ollama if not running
ollama serve

# Test API directly
curl http://localhost:11434/api/tags
```

**Problem**: Model not found
**Solution**:
```bash
# Download the model
ollama pull llama-guard3:1b

# Verify it's installed
ollama list
```

**Problem**: Analysis takes too long
**Solution**:
- First run is always slower (model loading)
- Consider using 1B model instead of 8B for faster responses
- Increase timeout in configuration if needed

### Getting Help

1. **Check the solution guide** above for reference implementations
2. **Review error messages** carefully - they often contain helpful information
3. **Test components individually** - verify nopCommerce and Ollama work separately
4. **Use logging** - Add `_output.WriteLine()` statements to debug your code

## Troubleshooting

### nopCommerce Issues
1. **nopCommerce not responding**:
   - Make sure Docker containers are running: `docker compose ps`
   - Check if port 90 is accessible: `curl http://localhost:90`
   - Wait for full startup (can take 2-3 minutes on first run)

### Selenium Issues
2. **ChromeDriver issues**:
   - The project includes Selenium.WebDriver.ChromeDriver package which automatically manages the driver
   - Make sure Chrome browser is installed on your system

3. **Test timeouts**:
   - nopCommerce can be slow on first startup
   - Increase the timeout if needed by modifying `TimeoutSeconds` constant

### Llama-Guard Issues
4. **Ollama not responding**:
   - Check if Ollama is running: `ollama list`
   - Start Ollama service: `ollama serve`
   - Verify API endpoint: `curl http://localhost:11434/api/tags`

5. **Model not found**:
   - Download required model: `ollama pull llama-guard3:1b`
   - Check available models: `ollama list`

6. **Content moderation timeouts**:
   - Llama-Guard can be slow on first run (model loading)
   - Consider using the 1B model instead of 8B for faster responses
   - Increase `TimeoutSeconds` in `LlamaGuardConfig`

7. **Memory issues**:
   - Llama-Guard models require significant RAM
   - 1B model: ~2GB RAM, 8B model: ~8GB RAM
   - Close other applications if needed

## 📋 Assessment Criteria

### Part 1: Basic Selenium (Required - 70 points)
- **InitialiseWebDriver (20 points)**: Proper Chrome configuration and driver setup
- **OpenNopCommercePage (15 points)**: Correct navigation and wait implementation
- **VerifyPageTitle (15 points)**: Accurate title verification
- **VerifyLogoAltText (20 points)**: Element location and attribute verification
- **Code Quality (10 points)**: Clean code, proper error handling, following patterns

### Part 2: AI Integration (Optional - 30 bonus points)
- **Exercise 1 (5 points)**: Basic connectivity test
- **Exercise 2 (5 points)**: Theory-based content testing
- **Exercise 3 (5 points)**: Batch processing implementation
- **Exercise 4 (5 points)**: Context-aware analysis
- **Exercise 5 (5 points)**: Performance testing
- **Integration Quality (5 points)**: Proper async handling and error management

## 🎓 Learning Outcomes

After completing this exercise, students will have learned:

### Technical Skills
- **Selenium WebDriver fundamentals** - Browser automation basics
- **Test automation patterns** - Method extraction, explicit waits, element location
- **Async programming** - Handling asynchronous operations in tests
- **API integration** - Working with external services in test automation
- **Performance testing** - Measuring and validating response times

### Best Practices
- **Clean code principles** - Readable, maintainable test code
- **Error handling** - Robust test implementation
- **Resource management** - Proper cleanup and disposal
- **Test organization** - Logical test structure and naming

### Advanced Concepts (Optional)
- **AI integration** - Using AI services in test automation
- **Content moderation** - Automated content safety validation
- **Batch processing** - Efficient handling of multiple operations
- **Context-aware testing** - Understanding how context affects analysis

## 🚀 Next Steps

After completing this exercise, consider exploring:

1. **Page Object Model** - Implement more structured test organization
2. **Data-driven testing** - Use external data sources for test parameters
3. **Parallel test execution** - Run tests concurrently for better performance
4. **CI/CD integration** - Automate tests in build pipelines
5. **Cross-browser testing** - Test across different browsers
6. **Mobile testing** - Extend to mobile web testing with Appium
7. **API testing** - Combine UI and API test automation
8. **Advanced AI integration** - Explore other AI models and use cases

## 📚 Additional Resources

- [Selenium Documentation](https://selenium-python.readthedocs.io/)
- [xUnit Documentation](https://xunit.net/)
- [Ollama Documentation](https://ollama.ai/docs)
- [Chrome DevTools](https://developer.chrome.com/docs/devtools/)
- [CSS Selectors Reference](https://www.w3schools.com/cssref/css_selectors.asp)
