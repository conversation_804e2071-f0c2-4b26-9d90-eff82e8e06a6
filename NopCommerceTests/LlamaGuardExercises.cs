using Xunit;
using Xunit.Abstractions;
using NopCommerceTests.Services;
using NopCommerceTests.Configuration;

namespace NopCommerceTests;

public class LlamaGuardExercises
{
    private readonly ITestOutputHelper _output;
    private readonly LlamaGuardService _llamaGuard;

    public LlamaGuardExercises(ITestOutputHelper output)
    {
        _output = output;
        var httpClient = new HttpClient();
        _llamaGuard = new LlamaGuardService(httpClient);
    }

    [Fact]
    public async Task Exercise1_TestLlamaGuardConnectivity()
    {
        // TODO: Student Exercise 1 - Test Llama-Guard Connectivity
        // 1. Create a simple test content string like "Hello, this is a test message"
        // 2. Call _llamaGuard.AnalyzeContentAsync() with the test content
        // 3. Assert that the result is not null
        // 4. Assert that result.IsSafe is true
        // 5. Log the result using _output.WriteLine()
        
        // HINT: Use await when calling async methods
        // HINT: Use Assert.NotNull() and Assert.True()
        
        throw new NotImplementedException("Student must complete Exercise 1");
    }

    [Theory]
    [InlineData("Welcome to our online store!")]
    [InlineData("Buy the best products here")]
    [InlineData("Free shipping on all orders")]
    [InlineData("Customer service available 24/7")]
    public async Task Exercise2_TestEcommerceContent(string content)
    {
        // TODO: Student Exercise 2 - Test E-commerce Content Safety
        // 1. Analyze the provided content using Llama-Guard
        // 2. Assert that all e-commerce content is considered safe
        // 3. Log the analysis result for each content piece
        
        // This test should pass for all typical e-commerce content
        
        throw new NotImplementedException("Student must complete Exercise 2");
    }

    [Fact]
    public async Task Exercise3_BatchContentAnalysis()
    {
        // TODO: Student Exercise 3 - Batch Content Analysis
        // 1. Create a list of content strings (at least 3 different e-commerce messages)
        // 2. Use _llamaGuard.AnalyzeContentBatchAsync() to analyze all content at once
        // 3. Assert that all results are safe
        // 4. Log how many items were analyzed and their results
        
        // HINT: Create List<string> with multiple content items
        // HINT: Use foreach loop to check each result
        
        throw new NotImplementedException("Student must complete Exercise 3");
    }

    [Fact]
    public async Task Exercise4_ContentWithContext()
    {
        // TODO: Student Exercise 4 - Content Analysis with Context
        // 1. Analyze the same content with different contexts
        // 2. Test content: "Click here to buy now!"
        // 3. Analyze with context "product_description" 
        // 4. Analyze with context "email_marketing"
        // 5. Compare and log the results
        // 6. Assert both analyses return safe results
        
        // This demonstrates how context can affect content analysis
        
        throw new NotImplementedException("Student must complete Exercise 4");
    }

    [Fact]
    public async Task Exercise5_PerformanceTest()
    {
        // TODO: Student Exercise 5 - Performance Testing
        // 1. Create a simple content string
        // 2. Measure how long it takes to analyze the content
        // 3. Use Stopwatch to measure execution time
        // 4. Assert the analysis completes within 10 seconds
        // 5. Log the execution time
        
        // HINT: Use System.Diagnostics.Stopwatch
        // HINT: Start stopwatch before analysis, stop after
        // HINT: Use stopwatch.ElapsedMilliseconds
        
        throw new NotImplementedException("Student must complete Exercise 5");
    }
}
