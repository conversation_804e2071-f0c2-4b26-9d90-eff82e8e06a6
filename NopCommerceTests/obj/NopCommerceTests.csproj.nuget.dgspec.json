{"format": 1, "restore": {"/home/<USER>/WTC/QA-meta/exercise-1-gui/NopCommerceTests/NopCommerceTests.csproj": {}}, "projects": {"/home/<USER>/WTC/QA-meta/exercise-1-gui/NopCommerceTests/NopCommerceTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/WTC/QA-meta/exercise-1-gui/NopCommerceTests/NopCommerceTests.csproj", "projectName": "NopCommerceTests", "projectPath": "/home/<USER>/WTC/QA-meta/exercise-1-gui/NopCommerceTests/NopCommerceTests.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/WTC/QA-meta/exercise-1-gui/NopCommerceTests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.36.0, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[141.0.7390.5400, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.5.3, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/snap/dotnet-sdk/256/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}