# Quick Reference Guide

## 🚀 Essential Commands

### Start nopCommerce
```bash
docker compose up -d
```

### Run Your Test
```bash
cd NopCommerceTests
dotnet test --filter "HomePageUserJourney_ShouldVerifyTitleAndLogoAltText" --verbosity normal
```

### Build Project
```bash
dotnet build
```

## 🔧 Key Code Snippets

### Chrome Options Setup
```csharp
var chromeOptions = new ChromeOptions();
chromeOptions.AddArgument("--headless");
chromeOptions.AddArgument("--no-sandbox");
chromeOptions.AddArgument("--disable-dev-shm-usage");
```

### WebDriver Creation
```csharp
IWebDriver driver = new ChromeDriver(chromeOptions);
driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromMilliseconds(500);
```

### Navigation
```csharp
driver.Navigate().GoToUrl("http://localhost:90");
```

### Explicit Wait
```csharp
var wait = new WebDriverWait(driver, TimeSpan.FromSeconds(30));
wait.Until(d => d.Title.Length > 0);
```

### Element Location
```csharp
var element = wait.Until(driver => 
    driver.FindElement(By.CssSelector(".header-logo img")));
```

### Get Attribute
```csharp
var altText = element.GetAttribute("alt");
```

### Assertions
```csharp
Assert.Equal("Expected Value", actualValue);
Assert.Contains("substring", fullString);
```

## 🎯 Test Structure Pattern

```csharp
[Fact]
public void TestName()
{
    // Arrange
    IWebDriver driver = InitialiseWebDriver();

    // Act
    OpenNopCommercePage(driver);
    VerifyPageTitle(driver, "Expected Title");
    VerifyLogoAltText(driver, "Expected Alt Text");

    // Cleanup
    driver.Quit();
}
```

## 🔍 CSS Selectors

| Element | CSS Selector |
|---------|-------------|
| Logo Image | `.header-logo img` |
| Page Title | Use `driver.Title` property |
| Any element by class | `.className` |
| Any element by ID | `#elementId` |
| Any element by tag | `tagName` |

## 🐛 Common Issues & Solutions

### Issue: "session not created"
**Solution**: Make sure Chrome is installed and no other instances are running

### Issue: Element not found
**Solution**: Add explicit wait before finding element

### Issue: Test timeout
**Solution**: Increase wait time or check if nopCommerce is running

### Issue: Wrong title/alt text
**Solution**: Check actual values in browser developer tools

## 📝 Async/Await (Advanced)

```csharp
// For Llama-Guard exercises
[Fact]
public async Task AsyncTestExample()
{
    var result = await _llamaGuard.AnalyzeContentAsync("content");
    Assert.NotNull(result);
    Assert.True(result.IsSafe);
}
```

## 🎯 Expected Values

- **Page Title**: "Your store. Home page title"
- **Logo Alt Text**: "Your store name"
- **Base URL**: "http://localhost:90"
- **Logo CSS Selector**: ".header-logo img"

## 🏃‍♂️ Quick Test Commands

```bash
# Test basic Selenium
dotnet test --filter "NopCommerceHomePageTests"

# Test Llama-Guard (if implemented)
dotnet test --filter "LlamaGuardExercises"

# Run all tests
dotnet test --verbosity normal
```
