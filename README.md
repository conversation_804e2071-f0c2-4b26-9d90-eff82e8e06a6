# nopCommerce Selenium Tests

This project contains Selenium WebDriver tests for the nopCommerce e-commerce platform.

## Prerequisites

1. **.NET 8 SDK** - Make sure you have .NET 8 installed
2. **Docker** - For running nopCommerce
3. **Chrome Browser** - The tests use ChromeDriver

## Setup

1. **Start nopCommerce**:
   ```bash
   docker compose up -d
   ```
   
   Wait for nopCommerce to fully start up (it may take a few minutes on first run).

2. **Verify nopCommerce is running**:
   Open http://localhost:90 in your browser to confirm the site is accessible.

## Running the Tests

### Run all tests (headless mode):
```bash
cd NopCommerceTests
dotnet test
```

### Run specific test class:
```bash
cd NopCommerceTests
dotnet test --filter "FullyQualifiedName~NopCommerceHomePageTests"
```

### Run visible browser tests (to see the browser in action):
```bash
cd NopCommerceTests
dotnet test --filter "FullyQualifiedName~NopCommerceHomePageTestsVisible"
```

### Run with verbose output:
```bash
cd NopCommerceTests
dotnet test --verbosity normal
```

## Test Details

### HomePageUserJourney_ShouldVerifyTitleAndLogoAltText

This test performs the following user journey:

1. **Opens the home page** at http://localhost:90
2. **Verifies the page title** is "Your store. Home page title"
3. **Locates the nopCommerce logo image** using CSS selector `.header-logo img`
4. **Verifies the alt text** of the logo is "Your store name"
5. **Additional verification** ensures the logo source contains "logo.png"

### Test Classes

- **NopCommerceHomePageTests**: Runs in headless mode (no browser window visible)
- **NopCommerceHomePageTestsVisible**: Runs with visible browser window for debugging

## Configuration

The tests are configured with:
- **Timeout**: 30 seconds for element waits
- **Base URL**: http://localhost:90
- **Browser**: Chrome with appropriate options for both headless and visible modes

## Troubleshooting

1. **nopCommerce not responding**: 
   - Make sure Docker containers are running: `docker compose ps`
   - Check if port 90 is accessible: `curl http://localhost:90`

2. **ChromeDriver issues**:
   - The project includes Selenium.WebDriver.ChromeDriver package which automatically manages the driver
   - Make sure Chrome browser is installed on your system

3. **Test timeouts**:
   - nopCommerce can be slow on first startup
   - Increase the timeout if needed by modifying `TimeoutSeconds` constant

## Dependencies

- Selenium.WebDriver 4.36.0
- Selenium.WebDriver.ChromeDriver (latest)
- xUnit testing framework
- .NET 8
