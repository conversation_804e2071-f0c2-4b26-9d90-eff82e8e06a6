# nopCommerce Selenium Tests with Llama-Guard Integration

This project contains Selenium WebDriver tests for the nopCommerce e-commerce platform, enhanced with AI-powered content moderation using Llama-Guard via local Ollama.

## Prerequisites

1. **.NET 8 SDK** - Make sure you have .NET 8 installed
2. **Docker** - For running nopCommerce
3. **Chrome Browser** - The tests use ChromeDriver
4. **Ollama** - For running Llama-Guard locally
5. **Llama-Guard Model** - Downloaded via Ollama

## Setup

1. **Install and start Ollama**:
   ```bash
   # Install Ollama (if not already installed)
   curl -fsSL https://ollama.ai/install.sh | sh

   # Start Ollama service
   ollama serve
   ```

2. **Download Llama-Guard models**:
   ```bash
   # Download the lightweight 1B model (recommended for testing)
   ollama pull llama-guard3:1b

   # Or download the full 8B model (better accuracy, slower)
   ollama pull llama-guard3:8b
   ```

3. **Start nopCommerce**:
   ```bash
   docker compose up -d
   ```

   Wait for nopCommerce to fully start up (it may take a few minutes on first run).

4. **Verify services are running**:
   - nopCommerce: Open http://localhost:90 in your browser
   - Ollama: Check `ollama list` shows your models

## Running the Tests

### Basic Selenium Tests (without Llama-Guard):
```bash
cd NopCommerceTests
# Run original Selenium tests
dotnet test --filter "FullyQualifiedName~NopCommerceHomePageTests"

# Run visible browser tests
dotnet test --filter "FullyQualifiedName~NopCommerceHomePageTestsVisible"
```

### Content Validation Tests (Llama-Guard only):
```bash
cd NopCommerceTests
# Test Llama-Guard connectivity
dotnet test --filter "FullyQualifiedName~ContentValidationTests.LlamaGuard_ShouldBeAvailable"

# Run all content validation tests
dotnet test --filter "FullyQualifiedName~ContentValidationTests"
```

### Integrated Tests (Selenium + Llama-Guard):
```bash
cd NopCommerceTests
# Run integrated tests with content moderation
dotnet test --filter "FullyQualifiedName~NopCommerceTestsWithLlamaGuard"

# Run specific integrated test
dotnet test --filter "HomePageUserJourney_WithContentModeration_ShouldVerifyTitleLogoAndContentSafety"
```

### Run all tests:
```bash
cd NopCommerceTests
dotnet test --verbosity normal
```

## Test Details

### Basic Selenium Tests

#### HomePageUserJourney_ShouldVerifyTitleAndLogoAltText
This test performs the following user journey:

1. **Opens the home page** at http://localhost:90
2. **Verifies the page title** is "Your store. Home page title"
3. **Locates the nopCommerce logo image** using CSS selector `.header-logo img`
4. **Verifies the alt text** of the logo is "Your store name"
5. **Additional verification** ensures the logo source contains "logo.png"

### Llama-Guard Integration Tests

#### Content Validation Tests
- **LlamaGuard_ShouldBeAvailable**: Tests connectivity to local Ollama instance
- **AnalyzeEcommerceContent_ShouldBeSafe**: Validates typical e-commerce content
- **AnalyzeNavigationContent_ShouldBeSafe**: Tests navigation elements
- **AnalyzeBatchContent_ShouldProcessMultipleItems**: Batch processing test
- **TestContentModerationPerformance**: Performance benchmarking

#### Integrated Selenium + Llama-Guard Tests
- **HomePageUserJourney_WithContentModeration_ShouldVerifyTitleLogoAndContentSafety**:
  - Performs standard Selenium validations
  - Extracts page content (title, main text, navigation)
  - Analyzes content safety using Llama-Guard
  - Logs moderation results
  - Can fail tests on unsafe content (configurable)

### Test Classes

- **NopCommerceHomePageTests**: Basic Selenium tests (headless mode)
- **NopCommerceHomePageTestsVisible**: Basic Selenium tests (visible browser)
- **ContentValidationTests**: Pure Llama-Guard content validation tests
- **NopCommerceTestsWithLlamaGuard**: Integrated Selenium + Llama-Guard tests

## Configuration

### Selenium Configuration
- **Timeout**: 30 seconds for element waits
- **Base URL**: http://localhost:90
- **Browser**: Chrome with appropriate options for both headless and visible modes

### Llama-Guard Configuration
The `LlamaGuardConfig` class provides the following settings:

- **BaseUrl**: Ollama API endpoint (default: `http://localhost:11434`)
- **ModelName**: Llama-Guard model to use (default: `llama-guard3:1b`)
- **TimeoutSeconds**: API request timeout (default: 30 seconds)
- **EnableModeration**: Enable/disable content moderation (default: `true`)
- **FailOnUnsafeContent**: Fail tests when unsafe content is detected (default: `false`)
- **MaxContentLength**: Maximum content length to analyze (default: 10,000 characters)
- **LogResults**: Enable moderation result logging (default: `true`)
- **ContentTypesToAnalyze**: Types of content to analyze (page_title, page_text, etc.)
- **ExcludeSelectors**: CSS selectors to exclude from analysis (scripts, styles, etc.)

## Troubleshooting

### nopCommerce Issues
1. **nopCommerce not responding**:
   - Make sure Docker containers are running: `docker compose ps`
   - Check if port 90 is accessible: `curl http://localhost:90`
   - Wait for full startup (can take 2-3 minutes on first run)

### Selenium Issues
2. **ChromeDriver issues**:
   - The project includes Selenium.WebDriver.ChromeDriver package which automatically manages the driver
   - Make sure Chrome browser is installed on your system

3. **Test timeouts**:
   - nopCommerce can be slow on first startup
   - Increase the timeout if needed by modifying `TimeoutSeconds` constant

### Llama-Guard Issues
4. **Ollama not responding**:
   - Check if Ollama is running: `ollama list`
   - Start Ollama service: `ollama serve`
   - Verify API endpoint: `curl http://localhost:11434/api/tags`

5. **Model not found**:
   - Download required model: `ollama pull llama-guard3:1b`
   - Check available models: `ollama list`

6. **Content moderation timeouts**:
   - Llama-Guard can be slow on first run (model loading)
   - Consider using the 1B model instead of 8B for faster responses
   - Increase `TimeoutSeconds` in `LlamaGuardConfig`

7. **Memory issues**:
   - Llama-Guard models require significant RAM
   - 1B model: ~2GB RAM, 8B model: ~8GB RAM
   - Close other applications if needed

## Dependencies

### Core Dependencies
- **.NET 8** - Runtime and SDK
- **Selenium.WebDriver 4.36.0** - Web automation
- **Selenium.WebDriver.ChromeDriver** - Chrome driver management
- **xUnit** - Testing framework

### Llama-Guard Integration Dependencies
- **Microsoft.Extensions.Http 9.0.9** - HTTP client factory
- **Microsoft.Extensions.Logging.Console 9.0.9** - Logging infrastructure
- **Newtonsoft.Json 13.0.4** - JSON serialization
- **Ollama** - Local AI model serving (external dependency)

### External Services
- **nopCommerce** - E-commerce platform (Docker container)
- **Ollama** - Local AI model server
- **Llama-Guard 3** - Content moderation model (1B or 8B variants)

## Features

### Content Safety Analysis
- **Real-time content moderation** during test execution
- **Batch content analysis** for performance testing
- **Configurable safety thresholds** and failure modes
- **Detailed logging** of moderation results
- **Performance metrics** for moderation operations

### Integration Benefits
- **Automated content safety validation** in CI/CD pipelines
- **Early detection** of potentially problematic content
- **Compliance testing** for content policies
- **Quality assurance** for user-facing content
- **Regression testing** for content changes

### Supported Content Types
- Page titles and metadata
- Main page content and text
- Navigation elements
- Form labels and placeholders
- Error messages and notifications
- Product descriptions and content
