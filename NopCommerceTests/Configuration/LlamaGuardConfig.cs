namespace NopCommerceTests.Configuration;

/// <summary>
/// Configuration settings for Llama-Guard integration
/// </summary>
public class LlamaGuardConfig
{
    /// <summary>
    /// Base URL for Ollama API (default: http://localhost:11434)
    /// </summary>
    public string BaseUrl { get; set; } = "http://localhost:11434";

    /// <summary>
    /// Llama-Guard model name to use (default: llama-guard3:1b)
    /// </summary>
    public string ModelName { get; set; } = "llama-guard3:1b";

    /// <summary>
    /// Timeout for API requests in seconds (default: 30)
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Whether to enable content moderation in tests (default: true)
    /// </summary>
    public bool EnableModeration { get; set; } = true;

    /// <summary>
    /// Whether to fail tests when unsafe content is detected (default: false)
    /// </summary>
    public bool FailOnUnsafeContent { get; set; } = false;

    /// <summary>
    /// Maximum content length to analyze (default: 10000 characters)
    /// </summary>
    public int MaxContentLength { get; set; } = 10000;

    /// <summary>
    /// Whether to log moderation results (default: true)
    /// </summary>
    public bool LogResults { get; set; } = true;

    /// <summary>
    /// Content types to analyze (default: all)
    /// </summary>
    public List<string> ContentTypesToAnalyze { get; set; } = new()
    {
        "page_title",
        "page_text",
        "form_content",
        "error_messages",
        "user_input"
    };

    /// <summary>
    /// CSS selectors to exclude from content analysis
    /// </summary>
    public List<string> ExcludeSelectors { get; set; } = new()
    {
        "script",
        "style",
        ".hidden",
        "[style*='display: none']"
    };
}
