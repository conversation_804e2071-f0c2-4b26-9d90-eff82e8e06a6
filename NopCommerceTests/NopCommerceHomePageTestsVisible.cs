using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Xunit;

namespace NopCommerceTests;

public class NopCommerceHomePageTestsVisible : IDisposable
{
    private readonly IWebDriver _driver;
    private readonly WebDriverWait _wait;
    private const string BaseUrl = "http://localhost:90";
    private const int TimeoutSeconds = 30;

    public NopCommerceHomePageTestsVisible()
    {
        // Configure Chrome options for visible mode (browser window will be shown)
        var chromeOptions = new ChromeOptions();
        // Remove headless mode to see the browser
        chromeOptions.AddArgument("--no-sandbox");
        chromeOptions.AddArgument("--disable-dev-shm-usage");
        chromeOptions.AddArgument("--disable-gpu");
        chromeOptions.AddArgument("--window-size=1920,1080");

        _driver = new ChromeDriver(chromeOptions);
        _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(TimeoutSeconds));
    }

    [Fact]
    public void HomePageUserJourney_ShouldVerifyTitleAndLogoAltText_VisibleBrowser()
    {
        // Step 1: Open the home page of the nopCommerce system at http://localhost:90
        _driver.Navigate().GoToUrl(BaseUrl);

        // Step 2: Verify the title of the page
        // Wait for the page to load completely
        _wait.Until(driver => driver.Title.Length > 0);
        
        var actualTitle = _driver.Title;
        Assert.Equal("Your store. Home page title", actualTitle);

        // Step 3: Locate the nopCommerce logo image and verify that the alt text is "Your store name"
        // Wait for the logo to be present
        var logoElement = _wait.Until(driver => 
            driver.FindElement(By.CssSelector(".header-logo img")));

        // Verify the alt text
        var altText = logoElement.GetAttribute("alt");
        Assert.Equal("Your store name", altText);

        // Additional verification: Ensure the logo image source is correct
        var logoSrc = logoElement.GetAttribute("src");
        Assert.Contains("logo.png", logoSrc);

        // Add a small delay to see the result before closing (optional)
        Thread.Sleep(2000);
    }

    public void Dispose()
    {
        _driver?.Quit();
        _driver?.Dispose();
    }
}
