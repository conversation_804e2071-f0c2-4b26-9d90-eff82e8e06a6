using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Xunit;

namespace NopCommerceTests;

public class NopCommerceHomePageTests
{
    private const string BaseUrl = "http://localhost:90";
    private const int TimeoutSeconds = 30;

    [Fact]
    public void HomePageUserJourney_ShouldVerifyTitleAndLogoAltText()
    {
        IWebDriver driver = InitialiseWebDriver();

        OpenNopCommercePage(driver);
        VerifyPageTitle(driver, "Your store. Home page title");
        VerifyLogoAltText(driver, "Your store name");

        driver.Quit();
    }

    private static IWebDriver InitialiseWebDriver()
    {
        // TODO: Student Task 1.1 - Complete this method
        // 1. Create ChromeOptions
        // 2. Add necessary Chrome arguments (headless, no-sandbox, etc.)
        // 3. Create ChromeDriver with options
        // 4. Set implicit wait timeout to 500 milliseconds
        // 5. Return the driver

        throw new NotImplementedException("Student must implement InitialiseWebDriver method");
    }

    private static void OpenNopCommercePage(IWebDriver driver)
    {
        // TODO: Student Task 1.2 - Complete this method
        // 1. Navigate to BaseUrl using driver.Navigate().GoToUrl()
        // 2. Create WebDriverWait with TimeoutSeconds
        // 3. Wait until page title length > 0

        throw new NotImplementedException("Student must implement OpenNopCommercePage method");
    }

    private static void VerifyPageTitle(IWebDriver driver, string expectedTitle)
    {
        // TODO: Student Task 1.3 - Complete this method
        // 1. Get the current page title from driver
        // 2. Use Assert.Equal to verify it matches expectedTitle

        throw new NotImplementedException("Student must implement VerifyPageTitle method");
    }

    private static void VerifyLogoAltText(IWebDriver driver, string expectedAltText)
    {
        // TODO: Student Task 1.4 - Complete this method
        // 1. Create WebDriverWait with TimeoutSeconds
        // 2. Wait for logo element using CSS selector ".header-logo img"
        // 3. Get the "alt" attribute from the logo element
        // 4. Use Assert.Equal to verify it matches expectedAltText
        // 5. BONUS: Verify the "src" attribute contains "logo.png"

        throw new NotImplementedException("Student must implement VerifyLogoAltText method");
    }
}