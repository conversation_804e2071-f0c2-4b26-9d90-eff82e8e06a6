# Llama-Guard Integration Guide

This document provides detailed information about the Llama-Guard integration in the nopCommerce Selenium tests.

## Overview

The integration combines Selenium WebDriver automation with AI-powered content moderation using Meta's Llama-Guard model running locally via Ollama. This enables automated detection of potentially unsafe or inappropriate content during web testing.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Selenium      │    │  LlamaGuard     │    │     <PERSON>llama      │
│   WebDriver     │───▶│   Service       │───▶│   (Local API)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Web Content    │    │  Content        │    │  Llama-Guard    │
│  Extraction     │    │  Analysis       │    │  Model (1B/8B)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Key Components

### 1. LlamaGuardService
- **Purpose**: Handles communication with Ollama API
- **Features**: 
  - Async content analysis
  - Batch processing
  - Error handling and retries
  - Performance logging
  - Configurable timeouts

### 2. LlamaGuardConfig
- **Purpose**: Configuration management
- **Settings**:
  - API endpoints and model selection
  - Content analysis parameters
  - Failure handling modes
  - Logging preferences

### 3. ContentModerationResult
- **Purpose**: Structured result object
- **Properties**:
  - Safety assessment (safe/unsafe)
  - Confidence score
  - Violation categories
  - Raw model response
  - Analysis metadata

## Usage Examples

### Basic Content Analysis

```csharp
// Initialize service
var httpClient = new HttpClient();
var llamaGuard = new LlamaGuardService(httpClient);

// Analyze content
var result = await llamaGuard.AnalyzeContentAsync(
    "Welcome to our online store!", 
    "homepage_content"
);

// Check result
if (result.IsSafe)
{
    Console.WriteLine($"Content is safe (confidence: {result.Confidence:P1})");
}
else
{
    Console.WriteLine($"Unsafe content detected: {result.Reason}");
    Console.WriteLine($"Categories: {string.Join(", ", result.Categories)}");
}
```

### Selenium Integration

```csharp
[Fact]
public async Task TestPageContentSafety()
{
    // Navigate to page
    _driver.Navigate().GoToUrl("http://localhost:90");
    
    // Extract content
    var pageTitle = _driver.Title;
    var bodyText = _driver.FindElement(By.TagName("body")).Text;
    
    // Analyze with Llama-Guard
    var titleResult = await _llamaGuard.AnalyzeContentAsync(pageTitle, "page_title");
    var bodyResult = await _llamaGuard.AnalyzeContentAsync(bodyText, "page_content");
    
    // Assert safety
    Assert.True(titleResult.IsSafe, $"Page title flagged: {titleResult.Reason}");
    Assert.True(bodyResult.IsSafe, $"Page content flagged: {bodyResult.Reason}");
}
```

### Batch Processing

```csharp
var contents = new[]
{
    "Product description 1",
    "Product description 2", 
    "Navigation menu text",
    "Footer content"
};

var results = await _llamaGuard.AnalyzeContentBatchAsync(contents, "product_content");

foreach (var result in results)
{
    _output.WriteLine($"{(result.IsSafe ? "✓" : "✗")} {result.OriginalContent}");
}
```

## Configuration Options

### Environment Variables
```bash
export OLLAMA_BASE_URL="http://localhost:11434"
export LLAMA_GUARD_MODEL="llama-guard3:1b"
export CONTENT_MODERATION_ENABLED="true"
export FAIL_ON_UNSAFE_CONTENT="false"
```

### Code Configuration
```csharp
var config = new LlamaGuardConfig
{
    BaseUrl = "http://localhost:11434",
    ModelName = "llama-guard3:1b",
    TimeoutSeconds = 30,
    EnableModeration = true,
    FailOnUnsafeContent = false,
    MaxContentLength = 10000,
    LogResults = true
};
```

## Content Categories

Llama-Guard classifies content into these safety categories:

- **S1**: Violent Crimes
- **S2**: Non-Violent Crimes
- **S3**: Sex Crimes
- **S4**: Child Exploitation
- **S5**: Defamation
- **S6**: Specialized Advice
- **S7**: Privacy
- **S8**: Intellectual Property
- **S9**: Indiscriminate Weapons
- **S10**: Hate
- **S11**: Self-Harm
- **S12**: Sexual Content
- **S13**: Elections

## Performance Considerations

### Model Selection
- **llama-guard3:1b**: Faster, lower memory (~2GB RAM), good for CI/CD
- **llama-guard3:8b**: More accurate, higher memory (~8GB RAM), better for thorough testing

### Optimization Tips
1. **Use batch processing** for multiple content pieces
2. **Cache results** for repeated content
3. **Set appropriate timeouts** based on model size
4. **Monitor memory usage** during test execution
5. **Consider model warm-up** for consistent performance

### Typical Performance
- **1B Model**: 500-2000ms per request (after warm-up)
- **8B Model**: 1000-5000ms per request (after warm-up)
- **Batch Processing**: 20-30% faster than individual requests

## Best Practices

### Test Organization
1. **Separate concerns**: Keep content validation tests separate from functional tests
2. **Use appropriate test categories**: Mark slow tests appropriately
3. **Implement retry logic**: Handle transient failures gracefully
4. **Log comprehensively**: Include context and timing information

### Content Extraction
1. **Clean extracted text**: Remove scripts, styles, and hidden elements
2. **Limit content length**: Avoid analyzing extremely long content
3. **Provide context**: Use meaningful context labels for better analysis
4. **Handle edge cases**: Empty content, special characters, etc.

### CI/CD Integration
1. **Use lightweight model**: llama-guard3:1b for faster builds
2. **Implement timeouts**: Prevent hanging builds
3. **Cache models**: Pre-download models in build images
4. **Parallel execution**: Run content validation in parallel with other tests

## Troubleshooting

### Common Issues

#### Model Loading Errors
```bash
# Check if model is available
ollama list

# Pull model if missing
ollama pull llama-guard3:1b
```

#### API Connection Issues
```bash
# Test Ollama API
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{"model": "llama-guard3:1b", "prompt": "Test", "stream": false}'
```

#### Memory Issues
- Monitor system memory during tests
- Use smaller model (1B instead of 8B)
- Reduce batch sizes
- Implement content length limits

### Debug Logging
Enable detailed logging to troubleshoot issues:

```csharp
var loggerFactory = LoggerFactory.Create(builder => 
    builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
var logger = loggerFactory.CreateLogger<LlamaGuardService>();

var llamaGuard = new LlamaGuardService(httpClient, logger: logger);
```

## Future Enhancements

### Planned Features
- **Custom safety categories** for domain-specific content
- **Content caching** to improve performance
- **Multi-model support** for different analysis types
- **Integration with other AI models** (GPT, Claude, etc.)
- **Advanced reporting** and analytics
- **Real-time monitoring** dashboards

### Integration Opportunities
- **API testing** with content validation
- **Mobile app testing** via Appium
- **Accessibility testing** with content analysis
- **Performance testing** with content quality metrics
