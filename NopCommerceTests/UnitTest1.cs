using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Xunit;

namespace NopCommerceTests;

public class NopCommerceHomePageTests
{
    private const string BaseUrl = "http://localhost:90";
    private const int TimeoutSeconds = 30;

    [Fact]
    public void HomePageUserJourney_ShouldVerifyTitleAndLogoAltText()
    {
        IWebDriver driver = InitialiseWebDriver();

        OpenNopCommercePage(driver);
        VerifyPageTitle(driver, "Your store. Home page title");
        VerifyLogoAltText(driver, "Your store name");

        driver.Quit();
    }

    private static IWebDriver InitialiseWebDriver()
    {
        var chromeOptions = new ChromeOptions();
        chromeOptions.AddArgument("--headless"); // Remove this line if you want to see the browser
        chromeOptions.AddArgument("--no-sandbox");
        chromeOptions.AddArgument("--disable-dev-shm-usage");
        chromeOptions.AddArgument("--disable-gpu");
        chromeOptions.AddArgument("--window-size=1920,1080");
        chromeOptions.AddArgument("--disable-extensions");
        chromeOptions.AddArgument("--disable-plugins");
        chromeOptions.AddArgument("--disable-default-apps");
        chromeOptions.AddArgument("--disable-background-timer-throttling");
        chromeOptions.AddArgument("--disable-backgrounding-occluded-windows");
        chromeOptions.AddArgument("--disable-renderer-backgrounding");
        chromeOptions.AddArgument("--disable-features=TranslateUI");
        chromeOptions.AddArgument("--disable-ipc-flooding-protection");

        // Add unique user data directory to avoid conflicts
        var tempDir = Path.Combine(Path.GetTempPath(), "chrome_test_" + Guid.NewGuid().ToString("N")[..8]);
        chromeOptions.AddArgument($"--user-data-dir={tempDir}");

        // Add remote debugging port to avoid conflicts
        var debugPort = new Random().Next(9222, 9999);
        chromeOptions.AddArgument($"--remote-debugging-port={debugPort}");

        IWebDriver driver = new ChromeDriver(chromeOptions);
        driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromMilliseconds(500);
        return driver;
    }

    private static void OpenNopCommercePage(IWebDriver driver)
    {
        driver.Navigate().GoToUrl(BaseUrl);

        // Wait for the page to load completely
        var wait = new WebDriverWait(driver, TimeSpan.FromSeconds(TimeoutSeconds));
        wait.Until(d => d.Title.Length > 0);
    }

    private static void VerifyPageTitle(IWebDriver driver, string expectedTitle)
    {
        var actualTitle = driver.Title;
        Assert.Equal(expectedTitle, actualTitle);
    }

    private static void VerifyLogoAltText(IWebDriver driver, string expectedAltText)
    {
        // Wait for the logo to be present
        var wait = new WebDriverWait(driver, TimeSpan.FromSeconds(TimeoutSeconds));
        var logoElement = wait.Until(driver =>
            driver.FindElement(By.CssSelector(".header-logo img")));

        // Verify the alt text
        var altText = logoElement.GetAttribute("alt");
        Assert.Equal(expectedAltText, altText);

        // Additional verification: Ensure the logo image source is correct
        var logoSrc = logoElement.GetAttribute("src");
        Assert.Contains("logo.png", logoSrc);
    }
}