using Xunit;
using Xunit.Abstractions;
using NopCommerceTests.Services;
using NopCommerceTests.Configuration;

namespace NopCommerceTests;

/// <summary>
/// Dedicated tests for content validation using Llama-Guard
/// </summary>
public class ContentValidationTests : IDisposable
{
    private readonly LlamaGuardService _llamaGuard;
    private readonly LlamaGuardConfig _config;
    private readonly ITestOutputHelper _output;
    private readonly HttpClient _httpClient;

    public ContentValidationTests(ITestOutputHelper output)
    {
        _output = output;
        _config = new LlamaGuardConfig();
        _httpClient = new HttpClient();
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
        _llamaGuard = new LlamaGuardService(_httpClient, _config.BaseUrl, _config.ModelName);
    }

    [Fact]
    public async Task LlamaGuard_ShouldBeAvailable()
    {
        // Test basic connectivity to Llama-Guard
        var testContent = "Hello, this is a test message.";
        var result = await _llamaGuard.AnalyzeContentAsync(testContent, "connectivity_test");
        
        Assert.NotNull(result);
        Assert.NotEmpty(result.RawResponse);
        Assert.True(result.IsSafe, "Basic test content should be safe");
        
        _output.WriteLine($"Llama-Guard is available and responded: {result.RawResponse}");
    }

    [Theory]
    [InlineData("Welcome to our online store!", true)]
    [InlineData("Buy computers and electronics here.", true)]
    [InlineData("Free shipping on orders over $50.", true)]
    [InlineData("Contact us for customer support.", true)]
    [InlineData("Your store name - Home page", true)]
    public async Task AnalyzeEcommerceContent_ShouldBeSafe(string content, bool expectedSafe)
    {
        var result = await _llamaGuard.AnalyzeContentAsync(content, "ecommerce_content");
        
        LogResult(result, content);
        
        Assert.NotNull(result);
        Assert.Equal(expectedSafe, result.IsSafe);
    }

    [Theory]
    [InlineData("Register", "navigation")]
    [InlineData("Log in", "navigation")]
    [InlineData("Shopping cart", "navigation")]
    [InlineData("Wishlist", "navigation")]
    [InlineData("Computers", "category")]
    [InlineData("Electronics", "category")]
    [InlineData("Apparel", "category")]
    [InlineData("Digital downloads", "category")]
    public async Task AnalyzeNavigationContent_ShouldBeSafe(string content, string contentType)
    {
        var result = await _llamaGuard.AnalyzeContentAsync(content, contentType);
        
        LogResult(result, content);
        
        Assert.NotNull(result);
        Assert.True(result.IsSafe, $"Navigation content '{content}' should be safe");
    }

    [Fact]
    public async Task AnalyzeBatchContent_ShouldProcessMultipleItems()
    {
        var contents = new[]
        {
            "Welcome to our store",
            "Featured products",
            "New online store is open!",
            "nopCommerce new release!",
            "About nopCommerce",
            "Community poll",
            "Do you like nopCommerce?"
        };

        var results = await _llamaGuard.AnalyzeContentBatchAsync(contents, "batch_test");
        
        Assert.Equal(contents.Length, results.Count);
        Assert.All(results, result => 
        {
            Assert.NotNull(result);
            Assert.NotEmpty(result.RawResponse);
        });

        foreach (var result in results)
        {
            LogResult(result, result.OriginalContent);
        }

        // Most e-commerce content should be safe
        var safeCount = results.Count(r => r.IsSafe);
        _output.WriteLine($"Batch analysis: {safeCount}/{results.Count} items marked as safe");
    }

    [Fact]
    public async Task AnalyzeProductDescriptions_ShouldBeSafe()
    {
        var productDescriptions = new[]
        {
            "Build your own computer with our custom PC builder",
            "Apple MacBook Pro 13-inch with Retina display",
            "HTC One M8 Android smartphone with 32GB memory",
            "$25 Virtual Gift Card for online purchases",
            "High-quality electronics and computer accessories"
        };

        foreach (var description in productDescriptions)
        {
            var result = await _llamaGuard.AnalyzeContentAsync(description, "product_description");
            
            LogResult(result, description);
            
            Assert.NotNull(result);
            Assert.True(result.IsSafe, $"Product description should be safe: {description}");
        }
    }

    [Fact]
    public async Task AnalyzeFormContent_ShouldBeSafe()
    {
        var formContents = new[]
        {
            "Enter your email here...",
            "Search store",
            "Subscribe to newsletter",
            "Add to cart",
            "Add to wishlist",
            "Add to compare list"
        };

        foreach (var content in formContents)
        {
            var result = await _llamaGuard.AnalyzeContentAsync(content, "form_content");
            
            LogResult(result, content);
            
            Assert.NotNull(result);
            Assert.True(result.IsSafe, $"Form content should be safe: {content}");
        }
    }

    [Fact]
    public async Task AnalyzeErrorMessages_ShouldBeSafe()
    {
        var errorMessages = new[]
        {
            "Please enter some search keyword",
            "Failed to add the product. Please refresh the page and try one more time.",
            "You have no items in your shopping cart.",
            "Please select an answer"
        };

        foreach (var message in errorMessages)
        {
            var result = await _llamaGuard.AnalyzeContentAsync(message, "error_message");
            
            LogResult(result, message);
            
            Assert.NotNull(result);
            Assert.True(result.IsSafe, $"Error message should be safe: {message}");
        }
    }

    [Fact]
    public async Task TestContentModerationPerformance()
    {
        var testContent = "This is a performance test for content moderation using Llama-Guard.";
        var iterations = 5;
        var times = new List<TimeSpan>();

        for (int i = 0; i < iterations; i++)
        {
            var startTime = DateTime.UtcNow;
            var result = await _llamaGuard.AnalyzeContentAsync(testContent, "performance_test");
            var endTime = DateTime.UtcNow;
            
            times.Add(endTime - startTime);
            
            Assert.NotNull(result);
            Assert.True(result.IsSafe);
        }

        var averageTime = TimeSpan.FromMilliseconds(times.Average(t => t.TotalMilliseconds));
        var maxTime = times.Max();
        var minTime = times.Min();

        _output.WriteLine($"Performance Results:");
        _output.WriteLine($"  Average: {averageTime.TotalMilliseconds:F2}ms");
        _output.WriteLine($"  Min: {minTime.TotalMilliseconds:F2}ms");
        _output.WriteLine($"  Max: {maxTime.TotalMilliseconds:F2}ms");

        // Assert reasonable performance (adjust threshold as needed)
        Assert.True(averageTime.TotalSeconds < 10, $"Average response time should be under 10 seconds, was {averageTime.TotalSeconds:F2}s");
    }

    [Fact]
    public async Task TestLlamaGuardErrorHandling()
    {
        // Test with very long content
        var longContent = new string('A', 50000);
        var result = await _llamaGuard.AnalyzeContentAsync(longContent, "long_content_test");
        
        Assert.NotNull(result);
        // Should either process successfully or handle gracefully
        
        LogResult(result, "Long content test");
    }

    private void LogResult(ContentModerationResult result, string content)
    {
        var status = result.IsSafe ? "✓ SAFE" : "✗ UNSAFE";
        var shortContent = content.Length > 50 ? content.Substring(0, 47) + "..." : content;
        
        _output.WriteLine($"{status} | {result.Confidence:P1} | {shortContent}");
        
        if (!result.IsSafe)
        {
            _output.WriteLine($"  Reason: {result.Reason}");
            if (result.Categories.Any())
            {
                _output.WriteLine($"  Categories: {string.Join(", ", result.Categories)}");
            }
        }
        
        _output.WriteLine($"  Raw: {result.RawResponse}");
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}
