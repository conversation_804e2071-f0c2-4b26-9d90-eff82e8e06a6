using System.Text;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;

namespace NopCommerceTests.Services;

public class LlamaGuardService
{
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl;
    private readonly string _modelName;
    private readonly ILogger<LlamaGuardService>? _logger;

    public LlamaGuardService(HttpClient httpClient, string baseUrl = "http://localhost:11434", string modelName = "llama-guard3:1b", ILogger<LlamaGuardService>? logger = null)
    {
        _httpClient = httpClient;
        _baseUrl = baseUrl;
        _modelName = modelName;
        _logger = logger;
    }

    /// <summary>
    /// Analyzes content for safety using Llama-Guard
    /// </summary>
    /// <param name="content">The content to analyze</param>
    /// <param name="context">Optional context for the content (e.g., "webpage content", "user input")</param>
    /// <returns>ContentModerationResult with safety assessment</returns>
    public async Task<ContentModerationResult> AnalyzeContentAsync(string content, string context = "content")
    {
        var startTime = DateTime.UtcNow;
        _logger?.LogDebug("Starting content analysis for {Context}. Content length: {Length}", context, content.Length);

        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(content))
            {
                _logger?.LogWarning("Empty or null content provided for analysis");
                return new ContentModerationResult
                {
                    IsSafe = true,
                    Confidence = 1.0,
                    Reason = "Empty content is considered safe",
                    OriginalContent = content,
                    Categories = new List<string>()
                };
            }

            var prompt = FormatPromptForLlamaGuard(content, context);
            var request = new OllamaRequest
            {
                Model = _modelName,
                Prompt = prompt,
                Stream = false
            };

            var json = JsonConvert.SerializeObject(request);
            var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

            _logger?.LogDebug("Sending request to Ollama API: {BaseUrl}/api/generate", _baseUrl);
            var response = await _httpClient.PostAsync($"{_baseUrl}/api/generate", httpContent);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger?.LogError("Ollama API returned error {StatusCode}: {Error}", response.StatusCode, errorContent);
                throw new HttpRequestException($"Ollama API error: {response.StatusCode} - {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var ollamaResponse = JsonConvert.DeserializeObject<OllamaResponse>(responseContent);

            if (ollamaResponse == null)
            {
                _logger?.LogError("Failed to deserialize Ollama response: {Response}", responseContent);
                throw new InvalidOperationException("Invalid response from Ollama API");
            }

            var result = ParseLlamaGuardResponse(ollamaResponse.Response ?? "", content);

            var duration = DateTime.UtcNow - startTime;
            _logger?.LogInformation("Content analysis completed in {Duration}ms. Result: {IsSafe} ({Confidence:P1})",
                duration.TotalMilliseconds, result.IsSafe, result.Confidence);

            return result;
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger?.LogError(ex, "Timeout during content analysis for {Context}", context);
            return new ContentModerationResult
            {
                IsSafe = false,
                Confidence = 0.0,
                Reason = "Analysis timed out - treating as unsafe",
                OriginalContent = content,
                Categories = new List<string> { "timeout" }
            };
        }
        catch (HttpRequestException ex)
        {
            _logger?.LogError(ex, "HTTP error during content analysis for {Context}", context);
            return new ContentModerationResult
            {
                IsSafe = false,
                Confidence = 0.0,
                Reason = $"Network error during content analysis: {ex.Message}",
                OriginalContent = content,
                Categories = new List<string> { "network_error" }
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Unexpected error during content analysis for {Context}", context);
            return new ContentModerationResult
            {
                IsSafe = false,
                Confidence = 0.0,
                Reason = $"Error during content analysis: {ex.Message}",
                OriginalContent = content,
                Categories = new List<string> { "error" }
            };
        }
    }

    /// <summary>
    /// Analyzes multiple pieces of content in batch
    /// </summary>
    public async Task<List<ContentModerationResult>> AnalyzeContentBatchAsync(IEnumerable<string> contents, string context = "content")
    {
        var tasks = contents.Select(content => AnalyzeContentAsync(content, context));
        var results = await Task.WhenAll(tasks);
        return results.ToList();
    }

    /// <summary>
    /// Formats the prompt according to Llama-Guard expectations
    /// </summary>
    private string FormatPromptForLlamaGuard(string content, string context)
    {
        // Llama-Guard expects a specific format for content moderation
        return $"User: {content}";
    }

    /// <summary>
    /// Parses the response from Llama-Guard
    /// </summary>
    private ContentModerationResult ParseLlamaGuardResponse(string response, string originalContent)
    {
        var result = new ContentModerationResult
        {
            OriginalContent = originalContent,
            RawResponse = response
        };

        // Llama-Guard typically responds with "safe" or lists violation categories
        var responseLower = response.ToLower().Trim();
        
        if (responseLower == "safe")
        {
            result.IsSafe = true;
            result.Confidence = 0.95;
            result.Reason = "Content is considered safe";
            result.Categories = new List<string>();
        }
        else
        {
            result.IsSafe = false;
            result.Confidence = 0.90;
            result.Reason = $"Content flagged by Llama-Guard: {response}";
            
            // Parse categories from response (Llama-Guard lists them as S1, S2, etc.)
            result.Categories = ParseViolationCategories(response);
        }

        return result;
    }

    /// <summary>
    /// Parses violation categories from Llama-Guard response
    /// </summary>
    private List<string> ParseViolationCategories(string response)
    {
        var categories = new List<string>();
        
        // Llama-Guard uses categories like S1, S2, S3, etc.
        var categoryMappings = new Dictionary<string, string>
        {
            ["S1"] = "Violent Crimes",
            ["S2"] = "Non-Violent Crimes", 
            ["S3"] = "Sex Crimes",
            ["S4"] = "Child Exploitation",
            ["S5"] = "Defamation",
            ["S6"] = "Specialized Advice",
            ["S7"] = "Privacy",
            ["S8"] = "Intellectual Property",
            ["S9"] = "Indiscriminate Weapons",
            ["S10"] = "Hate",
            ["S11"] = "Self-Harm",
            ["S12"] = "Sexual Content",
            ["S13"] = "Elections"
        };

        foreach (var mapping in categoryMappings)
        {
            if (response.Contains(mapping.Key))
            {
                categories.Add(mapping.Value);
            }
        }

        return categories.Any() ? categories : new List<string> { "unspecified_violation" };
    }
}

/// <summary>
/// Request model for Ollama API
/// </summary>
public class OllamaRequest
{
    [JsonProperty("model")]
    public string Model { get; set; } = "";

    [JsonProperty("prompt")]
    public string Prompt { get; set; } = "";

    [JsonProperty("stream")]
    public bool Stream { get; set; } = false;
}

/// <summary>
/// Response model from Ollama API
/// </summary>
public class OllamaResponse
{
    [JsonProperty("model")]
    public string Model { get; set; } = "";

    [JsonProperty("response")]
    public string Response { get; set; } = "";

    [JsonProperty("done")]
    public bool Done { get; set; }

    [JsonProperty("total_duration")]
    public long TotalDuration { get; set; }
}

/// <summary>
/// Result of content moderation analysis
/// </summary>
public class ContentModerationResult
{
    public bool IsSafe { get; set; }
    public double Confidence { get; set; }
    public string Reason { get; set; } = "";
    public string OriginalContent { get; set; } = "";
    public string RawResponse { get; set; } = "";
    public List<string> Categories { get; set; } = new();
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;
}
