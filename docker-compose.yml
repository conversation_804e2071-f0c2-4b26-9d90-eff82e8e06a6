version: "3.4"
services:
    nopcommerce_web:
        image: "nopcommerceteam/nopcommerce:4.40.4"
        container_name: nopcommerce_web
        ports:
            - "90:80"
        depends_on:
            - nopcommerce_database

    nopcommerce_database:
        image: "mcr.microsoft.com/mssql/server:2019-CU13-ubuntu-20.04"
        container_name: nopcommerce_mssql_server
        ports:
          - "1433:1433"
        environment:
            SA_PASSWORD: "nopCommerce_db_password"
            ACCEPT_EULA: "Y"
            MSSQL_PID: "Express"

volumes:
  nopcommerce_data: